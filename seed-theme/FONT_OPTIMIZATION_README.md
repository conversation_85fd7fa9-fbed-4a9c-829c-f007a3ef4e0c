# Оптимизация шрифтов Lato - Устранение моргания (FOIT)

## Обзор решения

Данная оптимизация полностью устраняет моргание шрифтов (FOIT - Flash of Invisible Text) при загрузке страницы, используя локальные файлы шрифтов Lato и современные техники оптимизации.

## Что было сделано

### 1. Локальные файлы шрифтов
- Скачаны оптимизированные файлы Lato в форматах WOFF2 и TTF
- Размещены в `assets/fonts/`:
  - `lato-regular-latin.woff2` (23KB)
  - `lato-bold-latin.woff2` (22KB)
  - `lato-regular.ttf` (70KB)
  - `lato-bold.ttf` (69KB)

### 2. Оптимизированные @font-face декларации
- Создан файл `assets/lato-fonts-local.css`
- Использует `font-display: swap` для предотвращения FOIT
- Включает fallback шрифты с похожими метриками

### 3. Preload оптимизация
- Добавлены preload теги в `layout/theme.liquid`
- Критические шрифты загружаются приоритетно
- Условная загрузка только для Lato шрифтов

### 4. Улучшенная fallback стратегия
- Обновлены все fallback стеки в `assets/screen-settings.css.liquid`
- Используются системные шрифты с похожими метриками
- Минимизирован layout shift при загрузке шрифтов

### 5. JavaScript оптимизация
- Создан `assets/font-loading-optimization.js`
- Использует FontFace API для контроля загрузки
- Включает fallback таймер (3 секунды)
- Сохраняет состояние в sessionStorage

### 6. Критический CSS
- Создан `snippets/critical-font-css.liquid`
- Инлайн CSS в head для немедленного отображения
- Плавные переходы между fallback и основными шрифтами

## Технические детали

### Устранение FOIT
1. **font-display: swap** - текст отображается немедленно с fallback шрифтами
2. **Preload критических шрифтов** - приоритетная загрузка WOFF2 файлов
3. **Критический CSS** - инлайн стили для немедленного рендеринга
4. **JavaScript контроль** - программное управление загрузкой шрифтов

### Оптимизация производительности
- **WOFF2 формат** - сжатие до 70% от оригинального размера
- **Локальный хостинг** - устранение внешних запросов
- **Условная загрузка** - preload только при использовании Lato
- **SessionStorage кеширование** - избежание повторной загрузки

### Fallback стратегия
```css
font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
```

## Результаты оптимизации

### До оптимизации:
- ❌ Моргание текста при загрузке (FOIT)
- ❌ Зависимость от Google Fonts CDN
- ❌ Медленная загрузка шрифтов
- ❌ Layout shift при смене шрифтов

### После оптимизации:
- ✅ Немедленное отображение текста
- ✅ Локальные оптимизированные шрифты
- ✅ Быстрая загрузка (WOFF2, preload)
- ✅ Минимальный layout shift
- ✅ Улучшенные Core Web Vitals

## Мониторинг и отладка

### Проверка загрузки шрифтов
```javascript
// В консоли браузера
document.fonts.ready.then(() => {
  console.log('Все шрифты загружены');
});

// Проверка состояния
console.log(document.documentElement.classList.contains('fonts-loaded'));
```

### Тестирование производительности
1. Откройте DevTools → Network
2. Отфильтруйте по "Font"
3. Проверьте время загрузки WOFF2 файлов
4. Убедитесь в отсутствии внешних запросов к Google Fonts

## Дополнительные рекомендации

### 1. Мониторинг Core Web Vitals
- Используйте Google PageSpeed Insights
- Проверяйте Cumulative Layout Shift (CLS)
- Мониторьте First Contentful Paint (FCP)

### 2. Тестирование на медленных соединениях
- Используйте Chrome DevTools → Network → Slow 3G
- Убедитесь, что текст отображается немедленно

### 3. Кроссбраузерная совместимость
- Тестируйте в Safari, Firefox, Chrome
- Проверяйте на мобильных устройствах

## Поддержка и обновления

### Обновление шрифтов
1. Скачайте новые версии с Google Fonts
2. Замените файлы в `assets/fonts/`
3. Обновите URL в CSS при необходимости

### Добавление новых весов шрифтов
1. Скачайте дополнительные веса (300, 500, 900)
2. Добавьте @font-face декларации в `lato-fonts-local.css`
3. Обновите preload теги в `theme.liquid`
4. Добавьте в JavaScript массив `fontsToLoad`

## Заключение

Данная оптимизация полностью устраняет моргание шрифтов Lato и значительно улучшает пользовательский опыт. Все изменения совместимы с существующей темой и не требуют дополнительной настройки.
