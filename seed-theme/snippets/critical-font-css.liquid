{%- comment -%}
  Critical Font CSS Snippet
  This CSS is inlined in the head to ensure immediate text visibility
  and prevent FOIT (Flash of Invisible Text)
{%- endcomment -%}

<style>
  /* Critical CSS for immediate font rendering */
  
  /* Ensure text is visible immediately with system fonts */
  body, 
  input, 
  textarea, 
  select, 
  button {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    font-display: swap;
  }

  /* Headings with system fonts initially */
  h1, h2, h3, h4, h5, h6,
  .h1, .h2, .h3, .h4, .h5, .h6 {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    font-display: swap;
  }

  /* Hide text briefly only if fonts are loading (prevents FOIT) */
  .font-loading body,
  .font-loading h1,
  .font-loading h2,
  .font-loading h3,
  .font-loading h4,
  .font-loading h5,
  .font-loading h6 {
    visibility: hidden;
  }

  /* Show text immediately with fallback fonts */
  body,
  h1, h2, h3, h4, h5, h6 {
    visibility: visible;
  }

  /* Apply Lato when fonts are loaded */
  .fonts-loaded body,
  .fonts-loaded input,
  .fonts-loaded textarea,
  .fonts-loaded select,
  .fonts-loaded button {
    font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  }

  .fonts-loaded h1,
  .fonts-loaded h2,
  .fonts-loaded h3,
  .fonts-loaded h4,
  .fonts-loaded h5,
  .fonts-loaded h6,
  .fonts-loaded .h1,
  .fonts-loaded .h2,
  .fonts-loaded .h3,
  .fonts-loaded .h4,
  .fonts-loaded .h5,
  .fonts-loaded .h6 {
    font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  }

  /* Smooth transition when fonts load */
  body,
  h1, h2, h3, h4, h5, h6,
  input, textarea, select, button {
    transition: font-family 0.1s ease-in-out;
  }

  /* Prevent layout shift with font metrics adjustment */
  @supports (font-size-adjust: 1) {
    body {
      font-size-adjust: 0.5;
    }
    
    .fonts-loaded body {
      font-size-adjust: auto;
    }
  }

  /* Optimize for Core Web Vitals */
  * {
    font-display: swap;
  }

  /* Ensure critical text is always visible */
  .critical-text,
  .product-title,
  .site-title,
  .navigation,
  .price {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif !important;
    visibility: visible !important;
  }

  .fonts-loaded .critical-text,
  .fonts-loaded .product-title,
  .fonts-loaded .site-title,
  .fonts-loaded .navigation,
  .fonts-loaded .price {
    font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif !important;
  }
</style>
