/**
 * Font Loading Optimization Script
 * Eliminates FOIT (Flash of Invisible Text) and optimizes font loading
 */

(function() {
  'use strict';

  // Check if fonts are already loaded
  if (document.documentElement.classList.contains('fonts-loaded')) {
    return;
  }

  // Font loading detection using FontFace API
  function loadFont(fontFamily, fontWeight, fontStyle) {
    return new Promise(function(resolve, reject) {
      if (!window.FontFace) {
        // Fallback for browsers without FontFace API
        resolve();
        return;
      }

      var fontUrl = '';
      var fontFormat = 'woff2';
      
      // Determine font URL based on weight
      if (fontWeight === '400' || fontWeight === 400) {
        fontUrl = window.Shopify ? 
          window.Shopify.routes.root + 'cdn/shop/files/lato-regular-latin.woff2' :
          '/assets/lato-regular-latin.woff2';
      } else if (fontWeight === '700' || fontWeight === 700) {
        fontUrl = window.Shopify ? 
          window.Shopify.routes.root + 'cdn/shop/files/lato-bold-latin.woff2' :
          '/assets/lato-bold-latin.woff2';
      }

      if (!fontUrl) {
        resolve();
        return;
      }

      var font = new FontFace(fontFamily, 'url(' + fontUrl + ') format("' + fontFormat + '")', {
        weight: fontWeight,
        style: fontStyle || 'normal',
        display: 'swap'
      });

      font.load().then(function(loadedFont) {
        document.fonts.add(loadedFont);
        resolve(loadedFont);
      }).catch(function(error) {
        console.warn('Font loading failed:', error);
        resolve(); // Don't reject, just continue
      });
    });
  }

  // Load critical fonts
  function loadCriticalFonts() {
    var fontsToLoad = [
      { family: 'Lato', weight: '400', style: 'normal' },
      { family: 'Lato', weight: '700', style: 'normal' }
    ];

    var fontPromises = fontsToLoad.map(function(font) {
      return loadFont(font.family, font.weight, font.style);
    });

    Promise.all(fontPromises).then(function() {
      // All fonts loaded successfully
      document.documentElement.classList.add('fonts-loaded');
      
      // Store in sessionStorage to avoid reloading on same session
      try {
        sessionStorage.setItem('fonts-loaded', 'true');
      } catch (e) {
        // Ignore storage errors
      }

      // Dispatch custom event
      if (window.CustomEvent) {
        var event = new CustomEvent('fontsLoaded', { detail: { fonts: fontsToLoad } });
        document.dispatchEvent(event);
      }
    }).catch(function(error) {
      console.warn('Some fonts failed to load:', error);
      // Still mark as loaded to prevent infinite loading
      document.documentElement.classList.add('fonts-loaded');
    });
  }

  // Check if fonts were loaded in this session
  try {
    if (sessionStorage.getItem('fonts-loaded') === 'true') {
      document.documentElement.classList.add('fonts-loaded');
      return;
    }
  } catch (e) {
    // Ignore storage errors
  }

  // Start loading fonts
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadCriticalFonts);
  } else {
    loadCriticalFonts();
  }

  // Fallback timeout - ensure fonts-loaded class is added even if loading fails
  setTimeout(function() {
    if (!document.documentElement.classList.contains('fonts-loaded')) {
      document.documentElement.classList.add('fonts-loaded');
      console.warn('Font loading timeout - using fallback fonts');
    }
  }, 3000); // 3 second timeout

})();
