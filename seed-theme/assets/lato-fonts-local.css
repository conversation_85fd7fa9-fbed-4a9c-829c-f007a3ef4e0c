/* 
 * Lato Font - Local Optimized Version
 * Eliminates FOIT (Flash of Invisible Text) by using local fonts with font-display: swap
 * Includes proper fallback fonts with similar metrics to minimize layout shift
 */

/* Lato Regular 400 - Latin */
@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 400;
  font-display: swap; /* Critical for eliminating FOIT */
  src: url('{{ "lato-regular-latin.woff2" | asset_url }}') format('woff2'),
       url('{{ "lato-regular.ttf" | asset_url }}') format('truetype');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* Lato Bold 700 - Latin */
@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 700;
  font-display: swap; /* Critical for eliminating FOIT */
  src: url('{{ "lato-bold-latin.woff2" | asset_url }}') format('woff2'),
       url('{{ "lato-bold.ttf" | asset_url }}') format('truetype');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* 
 * Fallback font stack with similar metrics to Lato
 * This minimizes layout shift when fonts are loading
 */
.lato-optimized {
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

/* 
 * Font loading optimization classes
 * Use these to control font loading behavior
 */
.font-loading {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

.font-loaded {
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

/* 
 * Critical CSS for immediate font rendering
 * This ensures text is visible immediately with fallback fonts
 */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

/* Apply Lato when loaded */
body.fonts-loaded {
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

/* 
 * Font metrics adjustment to minimize layout shift
 * These values are calculated to match Lato's metrics as closely as possible
 */
@font-face {
  font-family: 'Lato-fallback';
  src: local('Arial'), local('Helvetica Neue'), local('Helvetica'), local('sans-serif');
  font-display: swap;
  ascent-override: 95%;
  descent-override: 24%;
  line-gap-override: 0%;
  size-adjust: 107%;
}

/* Use fallback with adjusted metrics */
.lato-fallback {
  font-family: 'Lato-fallback', 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}
